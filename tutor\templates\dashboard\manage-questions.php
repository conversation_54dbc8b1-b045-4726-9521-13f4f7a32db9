<?php
/**
 * Instructor Question Management
 *
 * @package Tutor\Templates
 * @subpackage Dashboard
 * <AUTHOR> <<EMAIL>>
 * @link https://themeum.com
 * @since 1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

use TUTOR\Input;
use TUTOR\Instructor;
use TUTOR\Q_And_A;

// Eğitmen kontrolü
if ( ! tutor_utils()->is_instructor( null, true ) ) {
	tutor_utils()->tutor_empty_state( __( 'Bu sayfaya erişim yetkiniz bulunmamaktadır.', 'tutor' ) );
	return;
}

$question_id = Input::get( 'question_id', null, Input::TYPE_INT );
if ( $question_id ) {
	$question = tutor_utils()->get_qa_question( $question_id );
	$user_id  = get_current_user_id();

	if ( $question && ! Q_And_A::has_qna_access( $user_id, $question->comment_post_ID ) ) {
		tutor_utils()->tutor_empty_state( tutor_utils()->error_message() );
		return;
	}

	tutor_load_template_from_custom_path(
		tutor()->path . '/views/qna/qna-single.php',
		array(
			'question_id' => $question_id,
			'context'     => 'frontend-dashboard-qna-single',
		)
	);
	return;
}

// Eğitmen görünümü için sabit ayarlar
$view_as = 'instructor';
$qna_tabs = \Tutor\Q_And_A::tabs_key_value();
$active_tab = Input::get( 'tab', 'all' );
?>

<div class="tutor-frontend-dashboard-qna-header tutor-mb-32">
	<div class="tutor-row tutor-mb-24">
		<div class="tutor-col">
			<div class="tutor-fs-5 tutor-fw-medium tutor-color-black">
				<?php esc_html_e( 'Soruları Yönet', 'tutor' ); ?>
			</div>
			<div class="tutor-fs-7 tutor-color-muted tutor-mt-8">
				<?php esc_html_e( 'Kurslarınızla ilgili tüm öğrenci sorularını yönetin ve yanıtlayın.', 'tutor' ); ?>
			</div>
		</div>
	</div>

	<div class="tutor-row">
		<div class="tutor-col-lg-5">
			<div class="tutor-qna-filter tutor-d-flex tutor-align-center">
				<span class="tutor-fs-7 tutor-color-secondary tutor-mr-20"><?php esc_html_e( 'Sort By', 'tutor' ); ?>:</span>
				<div class="tutor-flex-grow-1">
					<select class="tutor-form-select tutor-select-redirector">
						<?php
						foreach ( $qna_tabs as $tab ) {
							$markup = '<option value="' . $tab['url'] . '" ' . ( $active_tab == $tab['key'] ? 'selected="selected"' : '' ) . '>
                                        ' . $tab['title'] . '(' . $tab['value'] . ')' . '
                                    </option>';
							echo wp_kses(
								$markup,
								array(
									'option' => array(
										'value'    => true,
										'selected' => true,
									),
								)
							);
						}
						?>
					</select>
				</div>
			</div>
		</div>
	</div>
</div>

<?php
$per_page     = tutor_utils()->get_option( 'pagination_per_page', 10 );
$current_page = max( 1, tutor_utils()->avalue_dot( 'current_page', $_GET ) );
$offset       = ( $current_page - 1 ) * $per_page;

$q_status    = Input::get( 'tab' );
// Eğitmen görünümü için asker_id null (tüm sorular)
$asker_id    = null;
$total_items = tutor_utils()->get_qa_questions( $offset, $per_page, '', null, null, $asker_id, $q_status, true );
$questions   = tutor_utils()->get_qa_questions( $offset, $per_page, '', null, null, $asker_id, $q_status );

tutor_load_template_from_custom_path(
	tutor()->path . '/views/qna/qna-table.php',
	array(
		'qna_list'       => $questions,
		'context'        => 'frontend-dashboard-qna-table-instructor',
		'view_as'        => $view_as,
		'qna_pagination' => array(
			'base'        => '?current_page=%#%',
			'total_items' => $total_items,
			'per_page'    => $per_page,
			'paged'       => $current_page,
		),
	)
);
?>
