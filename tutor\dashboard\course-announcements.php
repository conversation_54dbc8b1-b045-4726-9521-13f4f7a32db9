<?php
/**
 * Template for displaying Course Announcements for enrolled courses
 *
 * @package DmrLMS\Templates
 * @subpackage Dashboard
 * <AUTHOR> Developer
 * @since 1.0.6
 */

use TUTOR\Input;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

// Güvenlik kontrolü - kullanıcı giriş yapmış mı?
if ( ! is_user_logged_in() ) {
	tutor_utils()->tutor_empty_state( __( '<PERSON><PERSON><PERSON> Yapın', 'dmr-lms' ), __( 'Kurs duyurularını görüntülemek için giriş yapmanız gerekiyor.', 'dmr-lms' ) );
	return;
}

// Debug: Template yüklendiğini kontrol et
if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
	error_log( 'DMR LMS: Kurs Duyuruları template yüklendi - ' . date( 'Y-m-d H:i:s' ) );
}

// Ad<PERSON> kullan<PERSON>ılar için sistem durumu göster
if ( current_user_can( 'manage_options' ) && isset( $_GET['debug'] ) ) {
	$system_status = dmr_lms_check_announcements_system_status();
	echo '<div class="tutor-alert tutor-info tutor-mb-24">';
	echo '<h4>Kurs Duyuruları Sistem Durumu (Debug Mode)</h4>';
	echo '<ul>';
	echo '<li>Menü Eklendi: ' . ( $system_status['menu_added'] ? '✅' : '❌' ) . '</li>';
	echo '<li>Plugin Template Mevcut: ' . ( $system_status['template_exists'] ? '✅' : '❌' ) . '</li>';
	if ( isset( $system_status['template_path'] ) ) {
		echo '<li>Plugin Template Yolu: ' . esc_html( $system_status['template_path'] ) . '</li>';
	}
	echo '<li>Tema Template Mevcut: ' . ( $system_status['theme_template_exists'] ? '✅' : '❌' ) . '</li>';
	if ( isset( $system_status['theme_template_path'] ) ) {
		echo '<li>Tema Template Yolu: ' . esc_html( $system_status['theme_template_path'] ) . '</li>';
	}
	echo '<li>Fonksiyonlar Yüklü: ' . ( $system_status['functions_loaded'] ? '✅' : '❌' ) . '</li>';
	echo '<li>Cache Çalışıyor: ' . ( $system_status['cache_working'] ? '✅' : '❌' ) . '</li>';
	echo '<li>Kayıtlı Kurs Sayısı: ' . $system_status['user_enrolled_courses'] . '</li>';
	echo '<li>Toplam Duyuru Sayısı: ' . $system_status['total_announcements'] . '</li>';
	echo '<li>Mevcut URL: ' . esc_html( $_SERVER['REQUEST_URI'] ?? 'Bilinmiyor' ) . '</li>';
	echo '<li>Dashboard Page Slug: ' . esc_html( $wp_query->query_vars['tutor_dashboard_page'] ?? 'Yok' ) . '</li>';
	if ( ! empty( $system_status['errors'] ) ) {
		echo '<li>Hatalar: ' . implode( ', ', $system_status['errors'] ) . '</li>';
	}
	echo '</ul>';
	echo '</div>';
}

$per_page = tutor_utils()->get_option( 'pagination_per_page', 10 );
$paged    = max( 1, Input::get( 'current_page', 1, Input::TYPE_INT ) );

$order_filter  = Input::get( 'order', 'DESC' );
$search_filter = Input::get( 'search', '' );
$course_filter = Input::get( 'course-id', '' );
$date_filter   = Input::get( 'date', '' );

// Kullanıcının kayıtlı olduğu kursları al - cache ile optimize et
$user_id = get_current_user_id();
$cache_key = 'dmr_lms_enrolled_courses_' . $user_id;
$enrolled_course_ids = get_transient( $cache_key );

if ( false === $enrolled_course_ids ) {
	$enrolled_course_ids = tutor_utils()->get_enrolled_courses_ids_by_user( $user_id );
	// 30 dakika cache
	set_transient( $cache_key, $enrolled_course_ids, 30 * MINUTE_IN_SECONDS );
}

if ( empty( $enrolled_course_ids ) ) {
	$enrolled_course_ids = array( 0 ); // Boş sonuç için
}

// Tarih filtresi için
$year  = date( 'Y', strtotime( $date_filter ) );
$month = date( 'm', strtotime( $date_filter ) );
$day   = date( 'd', strtotime( $date_filter ) );

// Duyuru sorgusu - sadece kayıtlı olunan kurslardan
$args = array(
	'post_type'      => 'tutor_announcements',
	'post_status'    => 'publish',
	's'              => sanitize_text_field( $search_filter ),
	'post_parent__in' => $enrolled_course_ids, // Sadece kayıtlı olunan kurslar
	'posts_per_page' => sanitize_text_field( $per_page ),
	'paged'          => sanitize_text_field( $paged ),
	'orderby'        => 'date',
	'order'          => sanitize_text_field( $order_filter ),
	'meta_query'     => array(
		'relation' => 'AND',
	),
);

// Belirli bir kurs seçildiyse
if ( ! empty( $course_filter ) && in_array( $course_filter, $enrolled_course_ids ) ) {
	$args['post_parent'] = sanitize_text_field( $course_filter );
	unset( $args['post_parent__in'] );
}

// Tarih filtresi
if ( ! empty( $date_filter ) ) {
	$args['date_query'] = array(
		array(
			'year'  => $year,
			'month' => $month,
			'day'   => $day,
		),
	);
}

$the_query = new WP_Query( $args );

// Kayıtlı olunan kursları dropdown için al - cache ile optimize et
$enrolled_courses = array();
if ( ! empty( $enrolled_course_ids ) ) {
	$courses_cache_key = 'dmr_lms_enrolled_courses_data_' . $user_id;
	$enrolled_courses = get_transient( $courses_cache_key );

	if ( false === $enrolled_courses ) {
		$enrolled_courses_query = new WP_Query( array(
			'post_type'      => tutor()->course_post_type,
			'post_status'    => array( 'publish', 'private' ),
			'post__in'       => $enrolled_course_ids,
			'posts_per_page' => -1,
			'orderby'        => 'title',
			'order'          => 'ASC',
			'fields'         => 'ids', // Sadece ID'leri al, performans için
		) );

		if ( $enrolled_courses_query->have_posts() ) {
			// ID'lerden post objelerini oluştur
			$enrolled_courses = array();
			foreach ( $enrolled_courses_query->posts as $course_id ) {
				$course = get_post( $course_id );
				if ( $course ) {
					$enrolled_courses[] = $course;
				}
			}
		}
		wp_reset_postdata();

		// 1 saat cache
		set_transient( $courses_cache_key, $enrolled_courses, HOUR_IN_SECONDS );
	}
}

$image_base = tutor()->url . '/assets/images/';

// SEO için meta bilgileri ekle
add_action( 'wp_head', function() {
	echo '<meta name="description" content="' . esc_attr__( 'Kayıtlı olduğunuz kurslardaki tüm duyuruları görüntüleyin ve takip edin.', 'dmr-lms' ) . '">' . "\n";
	echo '<meta name="keywords" content="' . esc_attr__( 'kurs duyuruları, eğitim duyuruları, online kurs, LMS', 'dmr-lms' ) . '">' . "\n";
	echo '<meta property="og:title" content="' . esc_attr__( 'Kurs Duyuruları', 'dmr-lms' ) . '">' . "\n";
	echo '<meta property="og:description" content="' . esc_attr__( 'Kayıtlı olduğunuz kurslardaki tüm duyuruları görüntüleyin ve takip edin.', 'dmr-lms' ) . '">' . "\n";
	echo '<meta property="og:type" content="website">' . "\n";
}, 1 );
?>

<div class="dmr-course-announcements">
<div class="tutor-card tutor-p-24 dmr-course-announcements-header">
	<div class="tutor-row tutor-align-lg-center">
		<div class="tutor-col-lg-auto tutor-mb-16 tutor-mb-lg-0">
			<div class="tutor-round-box tutor-p-8" role="img" aria-label="<?php esc_attr_e( 'Kurs Duyuruları İkonu', 'dmr-lms' ); ?>">
				<i class="tutor-icon-bullhorn tutor-fs-3" aria-hidden="true"></i>
			</div>
		</div>

		<div class="tutor-col tutor-mb-16 tutor-mb-lg-0">
			<div class="tutor-fs-6 tutor-color-muted tutor-mb-4">
				<?php esc_html_e( 'Kurs Duyuruları', 'dmr-lms' ); ?>
			</div>
			<div class="tutor-fs-5 tutor-color-black">
				<?php esc_html_e( 'Kayıtlı olduğunuz kurslardaki tüm duyuruları görüntüleyin', 'dmr-lms' ); ?>
			</div>
		</div>
	</div>
</div>

<div class="tutor-card tutor-p-24 tutor-mt-24 dmr-course-announcements-filters">
	<div class="tutor-row tutor-align-center tutor-mb-16">
		<div class="tutor-col-md-3 tutor-mb-16 tutor-mb-md-0">
			<div class="tutor-form-group tutor-mb-0">
				<label for="course-filter" class="screen-reader-text"><?php esc_html_e( 'Kurs Filtresi', 'dmr-lms' ); ?></label>
				<select id="course-filter" class="tutor-form-select" data-search="course-id" aria-label="<?php esc_attr_e( 'Kursa göre filtrele', 'dmr-lms' ); ?>">
					<option value=""><?php esc_html_e( 'Tüm Kurslar', 'dmr-lms' ); ?></option>
					<?php if ( ! empty( $enrolled_courses ) ) : ?>
						<?php foreach ( $enrolled_courses as $course ) : ?>
							<option value="<?php echo esc_attr( $course->ID ); ?>" <?php selected( $course_filter, $course->ID ); ?>>
								<?php echo esc_html( $course->post_title ); ?>
							</option>
						<?php endforeach; ?>
					<?php endif; ?>
				</select>
			</div>
		</div>

		<div class="tutor-col-md-3 tutor-mb-16 tutor-mb-md-0">
			<div class="tutor-form-group tutor-mb-0">
				<label for="date-filter" class="screen-reader-text"><?php esc_html_e( 'Tarih Filtresi', 'dmr-lms' ); ?></label>
				<input id="date-filter" type="date" class="tutor-form-control" data-search="date" value="<?php echo esc_attr( $date_filter ); ?>" placeholder="<?php esc_html_e( 'Tarih Seçin', 'dmr-lms' ); ?>" aria-label="<?php esc_attr_e( 'Tarihe göre filtrele', 'dmr-lms' ); ?>">
			</div>
		</div>

		<div class="tutor-col-md-3 tutor-mb-16 tutor-mb-md-0">
			<div class="tutor-form-group tutor-mb-0">
				<label for="search-filter" class="screen-reader-text"><?php esc_html_e( 'Arama Filtresi', 'dmr-lms' ); ?></label>
				<input id="search-filter" type="search" class="tutor-form-control" data-search="search" value="<?php echo esc_attr( $search_filter ); ?>" placeholder="<?php esc_html_e( 'Duyuru Ara...', 'dmr-lms' ); ?>" aria-label="<?php esc_attr_e( 'Duyuru içeriğinde ara', 'dmr-lms' ); ?>">
			</div>
		</div>

		<div class="tutor-col-md-3">
			<div class="tutor-form-group tutor-mb-0">
				<label for="order-filter" class="screen-reader-text"><?php esc_html_e( 'Sıralama Filtresi', 'dmr-lms' ); ?></label>
				<select id="order-filter" class="tutor-form-select" data-search="order" aria-label="<?php esc_attr_e( 'Sıralama düzenini seç', 'dmr-lms' ); ?>">
					<option value="DESC" <?php selected( $order_filter, 'DESC' ); ?>><?php esc_html_e( 'En Yeni', 'dmr-lms' ); ?></option>
					<option value="ASC" <?php selected( $order_filter, 'ASC' ); ?>><?php esc_html_e( 'En Eski', 'dmr-lms' ); ?></option>
				</select>
			</div>
		</div>
	</div>
</div>

<?php
// Duyuruları listele
$announcements = $the_query->have_posts() ? $the_query->posts : array();

if ( ! empty( $announcements ) ) :
	?>
	<div class="tutor-card tutor-mt-24">
		<div class="tutor-card-header">
			<div class="tutor-fs-6 tutor-fw-medium tutor-color-black">
				<?php
				printf(
					esc_html__( '%d Duyuru Bulundu', 'dmr-lms' ),
					$the_query->found_posts
				);
				?>
			</div>
		</div>

		<div class="tutor-card-body">
			<?php foreach ( $announcements as $index => $announcement ) : ?>
				<?php
				// Performans için cache'lenmiş veri kullan
				$course_cache_key = 'dmr_course_data_' . $announcement->post_parent;
				$course = get_transient( $course_cache_key );
				if ( false === $course ) {
					$course = get_post( $announcement->post_parent );
					set_transient( $course_cache_key, $course, HOUR_IN_SECONDS );
				}

				$author_cache_key = 'dmr_author_data_' . $announcement->post_author;
				$author = get_transient( $author_cache_key );
				if ( false === $author ) {
					$author = get_userdata( $announcement->post_author );
					set_transient( $author_cache_key, $author, HOUR_IN_SECONDS );
				}

				$date_format = get_option( 'date_format' ) . ' ' . get_option( 'time_format' );
				?>
				<article class="tutor-card tutor-announcement-card tutor-mb-24"
				         data-announcement-id="<?php echo esc_attr( $announcement->ID ); ?>"
				         <?php echo $index >= 5 ? 'loading="lazy"' : ''; ?>>
					<header class="tutor-card-header tutor-d-block tutor-bg-gray-10">
						<div class="tutor-row tutor-align-center">
							<div class="tutor-col">
								<h3 class="tutor-card-title tutor-fs-6 tutor-fw-medium tutor-mb-4">
									<?php echo esc_html( $announcement->post_title ); ?>
								</h3>
								<div class="dmr-announcement-meta">
									<span>
										<i class="tutor-icon-course" aria-hidden="true"></i>
										<span class="screen-reader-text"><?php esc_html_e( 'Kurs:', 'dmr-lms' ); ?></span>
										<?php echo esc_html( $course ? $course->post_title : __( 'Bilinmeyen Kurs', 'dmr-lms' ) ); ?>
									</span>
									<span>
										<i class="tutor-icon-user" aria-hidden="true"></i>
										<span class="screen-reader-text"><?php esc_html_e( 'Yazar:', 'dmr-lms' ); ?></span>
										<?php echo esc_html( $author ? $author->display_name : __( 'Bilinmeyen Yazar', 'dmr-lms' ) ); ?>
									</span>
									<span>
										<i class="tutor-icon-calendar" aria-hidden="true"></i>
										<span class="screen-reader-text"><?php esc_html_e( 'Tarih:', 'dmr-lms' ); ?></span>
										<time datetime="<?php echo esc_attr( date( 'c', strtotime( $announcement->post_date ) ) ); ?>">
											<?php echo esc_html( date_i18n( $date_format, strtotime( $announcement->post_date ) ) ); ?>
										</time>
									</span>
								</div>
							</div>
						</div>
					</header>
					<div class="tutor-card-body">
						<div class="tutor-fs-6 tutor-color-secondary">
							<?php
							// İçerik uzunluğunu sınırla (performans için)
							$content = $announcement->post_content;
							if ( strlen( $content ) > 500 ) {
								$content = wp_trim_words( $content, 75, '...' );
							}
							echo wp_kses_post( wpautop( $content ) );
							?>
						</div>
					</div>
				</article>
			<?php endforeach; ?>
		</div>
	</div>

	<?php
	// Pagination
	if ( $the_query->max_num_pages > 1 ) :
		$pagination_data = array(
			'total_items' => $the_query->found_posts,
			'per_page'    => $per_page,
			'paged'       => $paged,
			'base'        => add_query_arg( 'current_page', '%#%' ),
		);

		$pagination_template = tutor()->path . '/views/elements/pagination.php';
		if ( file_exists( $pagination_template ) ) {
			tutor_load_template_from_custom_path( $pagination_template, $pagination_data );
		}
	endif;
	?>

<?php else : ?>
	<div class="tutor-card tutor-mt-24">
		<div class="tutor-card-body dmr-empty-announcements" role="status" aria-live="polite">
			<i class="tutor-icon-bullhorn" aria-hidden="true"></i>
			<h3><?php esc_html_e( 'Henüz Duyuru Yok', 'dmr-lms' ); ?></h3>
			<?php if ( empty( $enrolled_course_ids ) || ( count( $enrolled_course_ids ) === 1 && $enrolled_course_ids[0] === 0 ) ) : ?>
				<p><?php esc_html_e( 'Henüz hiçbir kursa kayıt olmadınız. Kurs duyurularını görmek için önce bir kursa kaydolun.', 'dmr-lms' ); ?></p>
				<a href="<?php echo esc_url( home_url( '/courses' ) ); ?>" class="tutor-btn tutor-btn-primary tutor-mt-16">
					<?php esc_html_e( 'Kursları Keşfet', 'dmr-lms' ); ?>
				</a>
			<?php else : ?>
				<p><?php esc_html_e( 'Kayıtlı olduğunuz kurslarda henüz duyuru bulunmuyor.', 'dmr-lms' ); ?></p>
				<p class="tutor-fs-7 tutor-color-muted tutor-mt-8">
					<?php
					printf(
						esc_html__( '%d kursa kayıtlısınız. Eğitmenler duyuru paylaştığında burada görünecektir.', 'dmr-lms' ),
						count( $enrolled_course_ids )
					);
					?>
				</p>
			<?php endif; ?>
		</div>
	</div>
<?php endif; ?>

<script>
jQuery(document).ready(function($) {
	'use strict';

	// Kurs duyuruları filtreleme sistemi - Modern ES6+ yaklaşımı
	const DmrCourseAnnouncementsFilter = {
		// Konfigürasyon
		config: {
			debounceDelay: 750,
			loadingTimeout: 5000,
			cacheExpiry: 300000 // 5 dakika
		},

		// Cache sistemi
		cache: new Map(),

		init() {
			this.bindEvents();
			this.initAccessibility();
			this.initPerformanceOptimizations();
		},

		bindEvents() {
			// Modern event delegation kullan
			$(document).on('change input', '[data-search]', this.handleFilterChange.bind(this));
			this.initSearchDebounce();
			this.initKeyboardNavigation();
		},

		handleFilterChange(event) {
			try {
				const $element = $(event.target);
				const searchParams = new URLSearchParams(window.location.search);
				const key = $element.data('search');
				const value = $element.val()?.trim() || '';

				// URL parametrelerini güncelle
				this.updateURLParams(searchParams, key, value);

				// Loading state ve analytics
				this.showLoadingState($element);
				this.trackFilterUsage(key, value);

				// Yeni URL'ye yönlendir
				window.location.search = searchParams.toString();
			} catch (error) {
				console.error('DMR LMS Filtre hatası:', error);
				this.showErrorMessage('Filtreleme sırasında bir hata oluştu.');
			}
		},

		updateURLParams(searchParams, key, value) {
			if (value) {
				searchParams.set(key, value);
			} else {
				searchParams.delete(key);
			}

			// Sayfa numarasını sıfırla
			searchParams.delete('current_page');

			// Timestamp ekle (cache busting için)
			searchParams.set('_t', Date.now());
		},

		initSearchDebounce() {
			let searchTimeout;

			$(document).on('input', '[data-search="search"]', (event) => {
				const $element = $(event.target);
				clearTimeout(searchTimeout);

				// Visual feedback
				$element.addClass('searching');

				searchTimeout = setTimeout(() => {
					$element.removeClass('searching');
					$element.trigger('change');
				}, this.config.debounceDelay);
			});
		},

		initKeyboardNavigation() {
			$(document).on('keydown', '[data-search]', (event) => {
				if (event.key === 'Enter' && $(event.target).is('input')) {
					event.preventDefault();
					$(event.target).trigger('change');
				}

				// Escape tuşu ile filtreleri temizle
				if (event.key === 'Escape') {
					this.clearAllFilters();
				}
			});
		},

		initAccessibility() {
			// ARIA attributes ve focus yönetimi
			$('[data-search]').each(function() {
				const $this = $(this);
				$this.attr('aria-describedby', $this.attr('id') + '-description');
			});

			// Focus yönetimi
			$(document).on('focus blur', '[data-search]', function(e) {
				$(this).attr('aria-expanded', e.type === 'focus');
			});
		},

		initPerformanceOptimizations() {
			// Intersection Observer ile lazy loading
			if ('IntersectionObserver' in window) {
				const observer = new IntersectionObserver((entries) => {
					entries.forEach(entry => {
						if (entry.isIntersecting) {
							entry.target.classList.add('visible');
						}
					});
				});

				document.querySelectorAll('.tutor-announcement-card').forEach(card => {
					observer.observe(card);
				});
			}
		},

		showLoadingState($element) {
			$element.prop('disabled', true).addClass('loading');

			setTimeout(() => {
				$element.prop('disabled', false).removeClass('loading');
			}, this.config.loadingTimeout);
		},

		showErrorMessage(message) {
			// Modern toast notification
			if (typeof tutor_toast !== 'undefined') {
				tutor_toast('error', message);
			} else if ('Notification' in window) {
				new Notification('DMR LMS', { body: message, icon: '/favicon.ico' });
			} else {
				console.error(message);
				alert(message);
			}
		},

		clearAllFilters() {
			const url = new URL(window.location);
			url.search = '';
			window.location.href = url.toString();
		},

		trackFilterUsage(key, value) {
			// Analytics tracking (Google Analytics, Matomo vb. için)
			if (typeof gtag !== 'undefined') {
				gtag('event', 'filter_used', {
					event_category: 'course_announcements',
					event_label: key,
					value: value ? 1 : 0
				});
			}
		}
	};

	// Sistemi güvenli şekilde başlat
	try {
		DmrCourseAnnouncementsFilter.init();

		// Performance monitoring
		if (window.performance && window.performance.mark) {
			performance.mark('dmr-announcements-filter-loaded');
		}

		// Debug log
		if (window.console?.log) {
			console.log('✅ DMR LMS: Kurs Duyuruları filtreleme sistemi başarıyla yüklendi');
		}

		// Global erişim için window'a ekle (debugging için)
		if (typeof window.dmrLMS === 'undefined') {
			window.dmrLMS = {};
		}
		window.dmrLMS.announcementsFilter = DmrCourseAnnouncementsFilter;

	} catch (error) {
		console.error('❌ DMR LMS: Kurs Duyuruları sistemi yüklenirken hata:', error);

		// Fallback: Basit form submission
		$('[data-search]').on('change', function() {
			const form = $('<form>', {
				method: 'GET',
				action: window.location.pathname
			});

			$('[data-search]').each(function() {
				const $input = $(this);
				const value = $input.val();
				if (value) {
					form.append($('<input>', {
						type: 'hidden',
						name: $input.data('search'),
						value: value
					}));
				}
			});

			$('body').append(form);
			form.submit();
		});
	}
});
</script>
</div> <!-- .dmr-course-announcements -->
