<?php
/**
 * Template for displaying Course Announcements for enrolled courses
 *
 * @package DmrLMS\Templates
 * @subpackage Dashboard
 * <AUTHOR> Developer
 * @since 1.0.5
 */

use TUTOR\Input;

if (!defined('ABSPATH')) {
    exit;
}

// SEO ve Accessibility için meta bilgiler
$current_user = wp_get_current_user();
$page_title_seo = sprintf(__('%s - <PERSON><PERSON>yuru<PERSON>', 'dmr-lms'), $current_user->display_name);
$page_description_seo = __('Kayıtlı olduğunuz kurslardaki güncel duyuruları takip edin. Eğitmenlerinizden gelen önemli bilgilendirmeleri kaçırmayın.', 'dmr-lms');

// Sayfa için structured data hazırla
$structured_data = array(
    '@context' => 'https://schema.org',
    '@type' => 'WebPage',
    'name' => $page_title_seo,
    'description' => $page_description_seo,
    'url' => tutor_utils()->get_tutor_dashboard_page_permalink('kurs-duyuruları'),
    'isPartOf' => array(
        '@type' => 'WebSite',
        'name' => get_bloginfo('name'),
        'url' => home_url()
    ),
    'breadcrumb' => array(
        '@type' => 'BreadcrumbList',
        'itemListElement' => array(
            array(
                '@type' => 'ListItem',
                'position' => 1,
                'name' => __('Dashboard', 'dmr-lms'),
                'item' => tutor_utils()->get_tutor_dashboard_page_permalink('index')
            ),
            array(
                '@type' => 'ListItem',
                'position' => 2,
                'name' => __('Kurs Duyuruları', 'dmr-lms'),
                'item' => tutor_utils()->get_tutor_dashboard_page_permalink('kurs-duyuruları')
            )
        )
    )
);

// Sayfa başlığı ve açıklama
$page_title = __('Kurs Duyuruları', 'dmr-lms');
$page_description = __('Kayıtlı olduğunuz kurslardaki tüm duyuruları buradan takip edebilirsiniz.', 'dmr-lms');

// Sayfalama ve filtreleme parametreleri
$per_page = tutor_utils()->get_option('pagination_per_page', 10);
$paged = max(1, Input::get('current_page', 1, Input::TYPE_INT));

$order_filter = Input::get('order', 'DESC');
$search_filter = Input::get('search', '');
$course_filter = Input::get('course-id', '');
$date_filter = Input::get('date', '');

// Tarih filtresi için
$year = date('Y', strtotime($date_filter));
$month = date('m', strtotime($date_filter));
$day = date('d', strtotime($date_filter));

// Kullanıcının kayıtlı olduğu kursları al - cache ile optimize et
$user_id = get_current_user_id();
$cache_key = 'dmr_lms_enrolled_courses_' . $user_id;
$enrolled_course_ids = get_transient($cache_key);

if (false === $enrolled_course_ids) {
    $enrolled_course_ids = tutor_utils()->get_enrolled_courses_ids_by_user($user_id);
    // 30 dakika cache
    set_transient($cache_key, $enrolled_course_ids, 30 * MINUTE_IN_SECONDS);
}

// Eğer kullanıcının kayıtlı olduğu kurs yoksa
if (empty($enrolled_course_ids)) {
    $enrolled_course_ids = array(0); // Boş sonuç için
}

// Duyuru sorgusu - sadece kayıtlı olunan kurslardan
$args = array(
    'post_type'      => 'tutor_announcements',
    'post_status'    => 'publish',
    's'              => sanitize_text_field($search_filter),
    'post_parent__in' => $enrolled_course_ids, // Sadece kayıtlı olunan kurslar
    'posts_per_page' => sanitize_text_field($per_page),
    'paged'          => sanitize_text_field($paged),
    'orderby'        => 'date',
    'order'          => sanitize_text_field($order_filter),
    'meta_query'     => array(
        'relation' => 'AND',
    ),
);

// Belirli bir kurs seçildiyse
if (!empty($course_filter) && in_array($course_filter, $enrolled_course_ids)) {
    $args['post_parent'] = sanitize_text_field($course_filter);
    unset($args['post_parent__in']);
}

// Tarih filtresi
if (!empty($date_filter)) {
    $args['date_query'] = array(
        array(
            'year'  => $year,
            'month' => $month,
            'day'   => $day,
        ),
    );
}

$the_query = new WP_Query($args);

// SEO için sayfa başlığını ayarla
add_filter('wp_title', function($title) use ($page_title_seo) {
    if (is_page() && isset($_GET['tutor_dashboard_page']) && $_GET['tutor_dashboard_page'] === 'kurs-duyuruları') {
        return $page_title_seo . ' | ' . get_bloginfo('name');
    }
    return $title;
});

// Meta description ekle
add_action('wp_head', function() use ($page_description_seo, $structured_data) {
    if (isset($_GET['tutor_dashboard_page']) && $_GET['tutor_dashboard_page'] === 'kurs-duyuruları') {
        echo '<meta name="description" content="' . esc_attr($page_description_seo) . '">' . "\n";
        echo '<meta name="robots" content="noindex, nofollow">' . "\n"; // Dashboard sayfası, arama motorlarında indexlenmemeli
        echo '<link rel="canonical" href="' . esc_url(tutor_utils()->get_tutor_dashboard_page_permalink('kurs-duyuruları')) . '">' . "\n";

        // Structured data ekle
        echo '<script type="application/ld+json">' . wp_json_encode($structured_data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>' . "\n";
    }
});

// Kayıtlı olunan kursları dropdown için al - cache ile optimize et
$enrolled_courses = array();
if (!empty($enrolled_course_ids) && $enrolled_course_ids[0] !== 0) {
    $courses_cache_key = 'dmr_lms_enrolled_courses_data_' . $user_id;
    $enrolled_courses = get_transient($courses_cache_key);

    if (false === $enrolled_courses) {
        $enrolled_courses_query = new WP_Query(array(
            'post_type'      => tutor()->course_post_type,
            'post_status'    => array('publish', 'private'),
            'post__in'       => $enrolled_course_ids,
            'posts_per_page' => -1,
            'orderby'        => 'title',
            'order'          => 'ASC',
            'fields'         => 'ids', // Sadece ID'leri al, performans için
        ));

        if ($enrolled_courses_query->have_posts()) {
            // ID'lerden post objelerini oluştur
            $enrolled_courses = array();
            foreach ($enrolled_courses_query->posts as $course_id) {
                $course = get_post($course_id);
                if ($course) {
                    $enrolled_courses[] = $course;
                }
            }
        }
        wp_reset_postdata();

        // 1 saat cache
        set_transient($courses_cache_key, $enrolled_courses, HOUR_IN_SECONDS);
    }
}
?>

<div class="tutor-fs-5 tutor-fw-medium tutor-color-black tutor-mb-24">
    <?php echo esc_html($page_title); ?>
</div>

<div class="tutor-fs-7 tutor-color-muted tutor-mb-32">
    <?php echo esc_html($page_description); ?>
</div>

<div class="tutor-card">
    <div class="tutor-card-header">
        <div class="dmr-announcements-header">
            <div class="dmr-announcements-title">
                <span class="tutor-fs-6 tutor-fw-medium tutor-color-black">
                    <?php esc_html_e('Duyurular', 'dmr-lms'); ?>
                </span>
                <span class="tutor-color-muted">
                    (<?php echo esc_html($the_query->found_posts); ?>)
                </span>
            </div>

            <div class="dmr-announcements-filters">
                <!-- Kurs Filtresi -->
                <div class="dmr-filter-item">
                    <select class="tutor-form-select" data-search="course-id">
                        <option value=""><?php esc_html_e('Tüm Kurslar', 'dmr-lms'); ?></option>
                        <?php if (!empty($enrolled_courses)) : ?>
                            <?php foreach ($enrolled_courses as $course) : ?>
                                <option value="<?php echo esc_attr($course->ID); ?>"
                                        <?php selected($course_filter, $course->ID); ?>>
                                    <?php echo esc_html($course->post_title); ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>

                <!-- Tarih Filtresi -->
                <div class="dmr-filter-item">
                    <input type="date" class="tutor-form-control"
                           data-search="date"
                           value="<?php echo esc_attr($date_filter); ?>"
                           placeholder="<?php esc_attr_e('Tarih Seçin', 'dmr-lms'); ?>">
                </div>

                <!-- Sıralama -->
                <div class="dmr-filter-item dmr-filter-sort">
                    <select class="tutor-form-select" data-search="order">
                        <option value="DESC" <?php selected($order_filter, 'DESC'); ?>>
                            <?php esc_html_e('En Yeni', 'dmr-lms'); ?>
                        </option>
                        <option value="ASC" <?php selected($order_filter, 'ASC'); ?>>
                            <?php esc_html_e('En Eski', 'dmr-lms'); ?>
                        </option>
                    </select>
                </div>

                <!-- Arama -->
                <div class="dmr-filter-item dmr-filter-search">
                    <div class="dmr-search-wrapper">
                        <span class="tutor-icon-search dmr-search-icon" aria-hidden="true"></span>
                        <input type="search" class="tutor-form-control"
                               data-search="search"
                               value="<?php echo esc_attr($search_filter); ?>"
                               placeholder="<?php esc_attr_e('Duyuru ara...', 'dmr-lms'); ?>">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="tutor-card-body">
        <?php if ($the_query->have_posts()) : ?>
            <?php
            $announcements = $the_query->posts;
            foreach ($announcements as $index => $announcement) :
                // Performans için cache'lenmiş veri kullan
                $course_cache_key = 'dmr_course_data_' . $announcement->post_parent;
                $course = get_transient($course_cache_key);
                if (false === $course) {
                    $course = get_post($announcement->post_parent);
                    set_transient($course_cache_key, $course, HOUR_IN_SECONDS);
                }

                $author_cache_key = 'dmr_author_data_' . $announcement->post_author;
                $author = get_transient($author_cache_key);
                if (false === $author) {
                    $author = get_userdata($announcement->post_author);
                    set_transient($author_cache_key, $author, HOUR_IN_SECONDS);
                }

                $date_format = get_option('date_format') . ' ' . get_option('time_format');
            ?>
                <article class="tutor-card tutor-announcement-card tutor-mb-24"
                         data-announcement-id="<?php echo esc_attr($announcement->ID); ?>"
                         <?php echo $index >= 5 ? 'loading="lazy"' : ''; ?>>
                    <header class="tutor-card-header tutor-d-block tutor-bg-gray-10">
                        <div class="tutor-row tutor-align-center">
                            <div class="tutor-col">
                                <h3 class="tutor-card-title tutor-fs-6 tutor-fw-medium tutor-mb-4">
                                    <?php echo esc_html($announcement->post_title); ?>
                                </h3>
                                <div class="dmr-announcement-meta">
                                    <span>
                                        <i class="tutor-icon-course" aria-hidden="true"></i>
                                        <span class="screen-reader-text"><?php esc_html_e('Kurs:', 'dmr-lms'); ?></span>
                                        <strong><?php echo esc_html($course ? $course->post_title : __('Bilinmeyen Kurs', 'dmr-lms')); ?></strong>
                                    </span>
                                    <span class="tutor-ml-16">
                                        <i class="tutor-icon-calendar" aria-hidden="true"></i>
                                        <span class="screen-reader-text"><?php esc_html_e('Tarih:', 'dmr-lms'); ?></span>
                                        <?php echo esc_html(wp_date($date_format, strtotime($announcement->post_date))); ?>
                                    </span>
                                    <?php if ($author) : ?>
                                        <span class="tutor-ml-16">
                                            <i class="tutor-icon-user" aria-hidden="true"></i>
                                            <span class="screen-reader-text"><?php esc_html_e('Yazar:', 'dmr-lms'); ?></span>
                                            <?php echo esc_html($author->display_name); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </header>

                    <div class="tutor-card-body">
                        <div class="tutor-fs-6 tutor-color-secondary tutor-announcement-content">
                            <?php
                            // Duyuru içeriğini güvenli şekilde göster
                            $content = wp_trim_words(strip_tags($announcement->post_content), 50, '...');
                            echo wp_kses_post(wpautop($content));
                            ?>
                        </div>
                        
                        <?php if (strlen(strip_tags($announcement->post_content)) > 300) : ?>
                            <div class="tutor-mt-16">
                                <button class="tutor-btn tutor-btn-outline-primary tutor-btn-sm dmr-read-more-btn" 
                                        data-announcement-id="<?php echo esc_attr($announcement->ID); ?>">
                                    <?php esc_html_e('Devamını Oku', 'dmr-lms'); ?>
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </article>
            <?php endforeach; ?>

            <!-- Sayfalama -->
            <?php
            $pagination_data = array(
                'total_items' => $the_query->found_posts,
                'per_page'    => $per_page,
                'paged'       => $paged,
            );
            
            $pagination_template_frontend = tutor()->path . 'templates/dashboard/elements/pagination.php';
            tutor_load_template_from_custom_path($pagination_template_frontend, $pagination_data);
            ?>

        <?php else : ?>
            <!-- Boş durum -->
            <div class="tutor-py-64">
                <?php tutor_utils()->tutor_empty_state(
                    empty($enrolled_course_ids) || $enrolled_course_ids[0] === 0 
                        ? __('Henüz hiçbir kursa kayıt olmadınız. Kurs duyurularını görmek için önce bir kursa kaydolun.', 'dmr-lms')
                        : __('Kayıtlı olduğunuz kurslarda henüz duyuru bulunmuyor.', 'dmr-lms')
                ); ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php wp_reset_postdata(); ?>

<!-- Duyuru detayları için modal -->
<div id="dmr-announcement-modal" class="tutor-modal-wrap" style="display: none;">
    <div class="tutor-modal-content">
        <div class="tutor-modal-header">
            <h4 class="tutor-modal-title"></h4>
            <button type="button" class="tutor-modal-close">
                <span class="tutor-icon-times" aria-hidden="true"></span>
            </button>
        </div>
        <div class="tutor-modal-body">
            <div class="dmr-announcement-full-content"></div>
        </div>
    </div>
</div>

<style>
/* Kurs duyuruları için özel stiller */
.dmr-announcement-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    font-size: 14px;
    color: #6b7280;
    align-items: center;
}

.dmr-announcement-meta i {
    margin-right: 4px;
    color: #9ca3af;
    font-size: 12px;
}

.tutor-announcement-content {
    line-height: 1.6;
    margin-top: 12px;
}

.dmr-read-more-btn {
    transition: all 0.2s ease;
    font-size: 14px;
}

.dmr-read-more-btn:hover {
    transform: translateY(-1px);
}

/* Yeni filtre alanı düzeltmeleri */
.dmr-announcements-header {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    width: 100%;
}

.dmr-announcements-title {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.dmr-announcements-filters {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    flex: 1;
    justify-content: flex-end;
}

.dmr-filter-item {
    display: flex;
    align-items: center;
    min-width: 140px;
}

.dmr-filter-item.dmr-filter-search {
    min-width: 200px;
    flex: 1;
    max-width: 300px;
}

.dmr-filter-item.dmr-filter-sort {
    min-width: 120px;
}

.dmr-filter-item .tutor-form-select,
.dmr-filter-item .tutor-form-control {
    height: 40px;
    font-size: 14px;
    width: 100%;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 8px 12px;
    background-color: #ffffff;
    transition: border-color 0.2s ease;
    margin: 0;
}

.dmr-filter-item .tutor-form-select:focus,
.dmr-filter-item .tutor-form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dmr-search-wrapper {
    position: relative;
    width: 100%;
}

.dmr-search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    z-index: 2;
    pointer-events: none;
    font-size: 14px;
}

.dmr-search-wrapper .tutor-form-control {
    padding-left: 40px;
}

/* Duyuru kartları için düzeltmeler */
.tutor-announcement-card {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    transition: box-shadow 0.2s ease;
}

.tutor-announcement-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tutor-announcement-card .tutor-card-header {
    padding: 16px 20px 12px;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.tutor-announcement-card .tutor-card-body {
    padding: 16px 20px;
}

.tutor-card-title {
    margin-bottom: 8px !important;
    font-weight: 600 !important;
    color: #111827;
}

/* Responsive düzeltmeler */
@media (max-width: 1200px) {
    .dmr-announcements-filters {
        gap: 8px;
    }

    .dmr-filter-item {
        min-width: 120px;
    }

    .dmr-filter-item.dmr-filter-search {
        min-width: 180px;
        max-width: 250px;
    }
}

@media (max-width: 992px) {
    .dmr-announcements-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .dmr-announcements-filters {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .dmr-filter-item {
        flex: 1;
        min-width: 140px;
    }

    .dmr-filter-item.dmr-filter-search {
        flex: 2;
        min-width: 200px;
        max-width: none;
    }
}

@media (max-width: 768px) {
    .dmr-announcement-meta {
        flex-direction: column;
        gap: 6px;
        align-items: flex-start;
    }

    .dmr-announcement-meta span {
        margin-left: 0 !important;
        display: flex;
        align-items: center;
    }

    .dmr-announcements-filters {
        flex-direction: column;
        gap: 12px;
        width: 100%;
    }

    .dmr-filter-item {
        width: 100%;
        min-width: auto;
    }

    .dmr-filter-item.dmr-filter-search {
        width: 100%;
        min-width: auto;
        max-width: none;
    }

    .tutor-announcement-card .tutor-card-header,
    .tutor-announcement-card .tutor-card-body {
        padding: 12px 16px;
    }
}

@media (max-width: 480px) {
    .tutor-card-header {
        padding: 16px 12px !important;
    }

    .tutor-card-body {
        padding: 16px 12px !important;
    }

    .dmr-filter-item .tutor-form-select,
    .dmr-filter-item .tutor-form-control {
        height: 44px;
        font-size: 16px; /* iOS zoom önleme */
    }

    .dmr-announcements-header {
        gap: 12px;
    }

    .dmr-announcements-filters {
        gap: 8px;
    }
}

/* Dark mode uyumluluğu */
[data-theme="dark"] .tutor-announcement-card {
    background-color: #1f2937;
    border-color: #374151;
}

[data-theme="dark"] .tutor-announcement-card .tutor-card-header {
    background-color: #111827;
    border-bottom-color: #374151;
}

[data-theme="dark"] .tutor-card-title {
    color: #f9fafb !important;
}

[data-theme="dark"] .dmr-announcement-meta {
    color: #9ca3af;
}

[data-theme="dark"] .dmr-announcement-meta i {
    color: #6b7280;
}

[data-theme="dark"] .tutor-announcement-content {
    color: #d1d5db;
}

[data-theme="dark"] .dmr-filter-item .tutor-form-select,
[data-theme="dark"] .dmr-filter-item .tutor-form-control {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

[data-theme="dark"] .dmr-filter-item .tutor-form-select:focus,
[data-theme="dark"] .dmr-filter-item .tutor-form-control:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

[data-theme="dark"] .dmr-search-icon {
    color: #6b7280;
}

/* Dark mode modal stilleri */
[data-theme="dark"] #dmr-announcement-modal .tutor-modal-content {
    background: #1f2937;
    border: 1px solid #374151;
}

[data-theme="dark"] #dmr-announcement-modal .tutor-modal-header {
    background-color: #111827;
    border-bottom-color: #374151;
}

[data-theme="dark"] #dmr-announcement-modal .tutor-modal-title {
    color: #f9fafb;
}

[data-theme="dark"] #dmr-announcement-modal .dmr-announcement-full-content {
    color: #d1d5db;
}

[data-theme="dark"] #dmr-announcement-modal .tutor-modal-close {
    color: #9ca3af;
}

[data-theme="dark"] #dmr-announcement-modal .tutor-modal-close:hover {
    color: #f3f4f6;
    background-color: #374151;
}

[data-theme="dark"] .tutor-spinner {
    border-color: #374151;
    border-top-color: #60a5fa;
}

/* Modal düzeltmeleri */
#dmr-announcement-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 99999;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

#dmr-announcement-modal.show {
    display: flex !important;
}

#dmr-announcement-modal .tutor-modal-content {
    background: white;
    border-radius: 12px;
    max-width: 700px;
    width: 100%;
    max-height: 85vh;
    overflow: hidden;
    position: relative;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

#dmr-announcement-modal .tutor-modal-header {
    padding: 24px 24px 16px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    background-color: #f9fafb;
    border-radius: 12px 12px 0 0;
}

#dmr-announcement-modal .tutor-modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    line-height: 1.4;
    flex: 1;
    padding-right: 16px;
}

#dmr-announcement-modal .tutor-modal-body {
    padding: 24px;
    overflow-y: auto;
    max-height: calc(85vh - 100px);
}

#dmr-announcement-modal .dmr-announcement-full-content {
    font-size: 15px;
    line-height: 1.7;
    color: #374151;
}

#dmr-announcement-modal .dmr-announcement-full-content p {
    margin-bottom: 16px;
}

#dmr-announcement-modal .dmr-announcement-full-content p:last-child {
    margin-bottom: 0;
}

#dmr-announcement-modal .tutor-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
}

#dmr-announcement-modal .tutor-modal-close:hover {
    color: #374151;
    background-color: #e5e7eb;
}

#dmr-announcement-modal .tutor-modal-close:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Loading spinner */
.tutor-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive modal */
@media (max-width: 768px) {
    #dmr-announcement-modal {
        padding: 16px;
    }

    #dmr-announcement-modal .tutor-modal-content {
        max-width: 100%;
        max-height: 90vh;
        border-radius: 8px;
    }

    #dmr-announcement-modal .tutor-modal-header {
        padding: 20px 20px 12px;
        border-radius: 8px 8px 0 0;
    }

    #dmr-announcement-modal .tutor-modal-title {
        font-size: 16px;
        padding-right: 12px;
    }

    #dmr-announcement-modal .tutor-modal-body {
        padding: 20px;
        max-height: calc(90vh - 80px);
    }

    #dmr-announcement-modal .tutor-modal-close {
        width: 36px;
        height: 36px;
        font-size: 20px;
    }
}
</style>

<script>
// JavaScript verilerini manuel olarak tanımla
window.dmr_course_announcements_data = {
    ajaxurl: '<?php echo esc_js(home_url('/wp-admin/admin-ajax.php')); ?>',
    nonce: '<?php echo esc_js(wp_create_nonce('dmr_announcement_nonce')); ?>',
    strings: {
        loading: '<?php echo esc_js(__('Yükleniyor...', 'dmr-lms')); ?>',
        error: '<?php echo esc_js(__('Bir hata oluştu. Lütfen tekrar deneyin.', 'dmr-lms')); ?>',
        close: '<?php echo esc_js(__('Kapat', 'dmr-lms')); ?>'
    }
};

document.addEventListener('DOMContentLoaded', function() {
    console.log('DMR Course Announcements: JavaScript yüklendi');

    // Modal elementlerini cache'le
    const modal = document.getElementById('dmr-announcement-modal');
    const modalTitle = modal?.querySelector('.tutor-modal-title');
    const modalContent = modal?.querySelector('.dmr-announcement-full-content');
    const modalClose = modal?.querySelector('.tutor-modal-close');

    if (!modal || !modalTitle || !modalContent) {
        console.warn('DMR Course Announcements: Modal elementleri bulunamadı');
        return;
    }

    console.log('DMR Course Announcements: Modal elementleri bulundu');

    // Event delegation kullanarak performansı artır
    document.addEventListener('click', function(e) {
        console.log('Tıklanan element:', e.target);

        // Devamını Oku butonları
        if (e.target.classList.contains('dmr-read-more-btn')) {
            e.preventDefault();
            const announcementId = e.target.getAttribute('data-announcement-id');
            if (announcementId) {
                loadAnnouncementDetails(announcementId);
            }
        }

        // Modal kapatma
        if (e.target.classList.contains('tutor-modal-close') ||
            e.target.closest('.tutor-modal-close') ||
            (e.target === modal && !e.target.closest('.tutor-modal-content'))) {
            closeModal();
        }
    });

    // ESC tuşu ile modal kapatma
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.classList.contains('show')) {
            closeModal();
        }
    });

    // Filtreleme işlevselliği
    const filterElements = document.querySelectorAll('[data-search]');
    filterElements.forEach(function(element) {
        element.addEventListener('change', function() {
            applyFilters();
        });

        // Arama inputu için keyup event
        if (element.type === 'search' || element.type === 'text') {
            let timeout;
            element.addEventListener('keyup', function() {
                clearTimeout(timeout);
                timeout = setTimeout(applyFilters, 500); // 500ms gecikme ile arama
            });
        }
    });

    function applyFilters() {
        const currentUrl = new URL(window.location);
        const params = new URLSearchParams(currentUrl.search);

        // Mevcut filtreleri temizle
        params.delete('course-id');
        params.delete('date');
        params.delete('order');
        params.delete('search');
        params.delete('current_page'); // Sayfa numarasını sıfırla

        // Yeni filtre değerlerini ekle
        filterElements.forEach(function(element) {
            const value = element.value.trim();
            const searchKey = element.getAttribute('data-search');

            if (value && searchKey) {
                params.set(searchKey, value);
            }
        });

        // Sayfayı yenile
        currentUrl.search = params.toString();
        window.location.href = currentUrl.toString();
    }

    function loadAnnouncementDetails(announcementId) {
        // Loading state
        modalTitle.textContent = window.dmr_course_announcements_data.strings.loading;
        modalContent.innerHTML = '<div class="tutor-text-center tutor-py-32"><div class="tutor-spinner"></div></div>';

        // Modal'ı göster
        showModal();

        // AJAX isteği - jQuery kullanarak daha uyumlu hale getir
        if (typeof jQuery !== 'undefined') {
            jQuery.ajax({
                url: window.dmr_course_announcements_data.ajaxurl,
                type: 'POST',
                data: {
                    action: 'dmr_get_announcement_details',
                    announcement_id: announcementId,
                    _wpnonce: window.dmr_course_announcements_data.nonce
                },
                success: function(data) {
                    if (data.success && data.data) {
                        modalTitle.textContent = data.data.title || '';
                        modalContent.innerHTML = data.data.content || '';

                        // Accessibility için focus yönetimi
                        modal.setAttribute('aria-hidden', 'false');
                        modalClose.focus();
                    } else {
                        throw new Error(data.data?.message || window.dmr_course_announcements_data.strings.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('DMR Course Announcements Error:', error);
                    modalTitle.textContent = window.dmr_course_announcements_data.strings.error;
                    modalContent.innerHTML = '<div class="tutor-alert tutor-alert-warning">' +
                                           window.dmr_course_announcements_data.strings.error +
                                           '</div>';
                }
            });
        } else {
            // jQuery yoksa fetch kullan
            fetch(window.dmr_course_announcements_data.ajaxurl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'dmr_get_announcement_details',
                    announcement_id: announcementId,
                    _wpnonce: window.dmr_course_announcements_data.nonce
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    modalTitle.textContent = data.data.title || '';
                    modalContent.innerHTML = data.data.content || '';

                    // Accessibility için focus yönetimi
                    modal.setAttribute('aria-hidden', 'false');
                    modalClose.focus();
                } else {
                    throw new Error(data.data?.message || window.dmr_course_announcements_data.strings.error);
                }
            })
            .catch(error => {
                console.error('DMR Course Announcements Error:', error);
                modalTitle.textContent = window.dmr_course_announcements_data.strings.error;
                modalContent.innerHTML = '<div class="tutor-alert tutor-alert-warning">' +
                                       window.dmr_course_announcements_data.strings.error +
                                       '</div>';
            });
        }
    }

    function showModal() {
        modal.classList.add('show');
        modal.style.display = 'flex';
        modal.setAttribute('aria-hidden', 'false');
        document.body.style.overflow = 'hidden'; // Scroll'u engelle
    }

    function closeModal() {
        modal.classList.remove('show');
        modal.setAttribute('aria-hidden', 'true');
        document.body.style.overflow = ''; // Scroll'u geri getir

        // Animasyon bitince gizle
        setTimeout(() => {
            if (!modal.classList.contains('show')) {
                modal.style.display = 'none';
            }
        }, 300);

        // Focus'u geri döndür
        const activeButton = document.querySelector('.dmr-read-more-btn:focus');
        if (activeButton) {
            activeButton.focus();
        }
    }
});
</script>
